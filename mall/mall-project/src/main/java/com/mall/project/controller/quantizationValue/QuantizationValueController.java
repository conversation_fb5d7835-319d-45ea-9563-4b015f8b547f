package com.mall.project.controller.quantizationValue;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.quantizationValue.QuantizationValue;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 量化值控制器
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class QuantizationValueController {

    @Autowired
    private QuantizationValueService quantizationValueService;

    /**
     * 量化值计算
     */
    @PostMapping("/updateQuantizationValue")
    public CommonResult<String> updateQuantizationValue() {
        quantizationValueService.updateQuantizationValue();
        return CommonResult.success("量化值计算成功");
    }

    /**
     * 查询量化值, 分页显示
     */
    @PostMapping("/queryQuantizationValuePages")
    public CommonResult<CommonPage<Map<String, Object>>> queryQuantizationValuePages(@RequestBody @Valid QuantizationValue param) {
        CommonPage<Map<String, Object>> commonPage = quantizationValueService.queryQuantizationValuePages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 导出量化值 Excel
     */
    @PostMapping("/exportQuantizationValueExcel")
    public void exportQuantizationValueExcel(HttpServletResponse response,@RequestBody @Valid QuantizationValue param) {
        try {
            // 获取量化值数据
            List<Map<String, Object>> dataList = quantizationValueService.exportQuantizationValueExcel(param.getPhone(),param.getStartDate(), param.getEndDate());

            // 获取汇总数据
            String adminDailyQuantifyValue = quantizationValueService.queryAdminDailyQuantifyValue(param.getStartDate());
            String adminDailyQuantizationValue = quantizationValueService.adminDailyQuantizationValue(param.getStartDate());
            String todayTotalPlatformGold = quantizationValueService.todayTotalPlatformGold(param.getPhone(),param.getStartDate());
            String totalPlatformGold = quantizationValueService.totalPlatformGold(param.getPhone(),param.getStartDate());
            String todayTotalPromotionGold = quantizationValueService.todayTotalPromotionGold(param.getPhone(),param.getStartDate());
            String totalPromotionGold = quantizationValueService.totalPromotionGold(param.getPhone(),param.getStartDate());
            String everydayTotalQuantizationValue = quantizationValueService.everydayTotalQuantizationValue(param.getPhone(),param.getStartDate());
            String allTotalQuantizationValue = quantizationValueService.allTotalQuantizationValue(param.getPhone(),param.getStartDate());
            String everydayTotalCreditValue = quantizationValueService.everydayTotalCreditValue(param.getPhone(),param.getStartDate());
            String allTotalCreditValue = quantizationValueService.allTotalCreditValue(param.getPhone(),param.getStartDate());

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("name", "name");
            fieldMapping.put("value", "value");
            fieldMapping.put("totalValue", "totalValue");
            fieldMapping.put("creditValue", "creditValue");
            fieldMapping.put("totalCreditValue", "totalCreditValue");
            fieldMapping.put("platformGold", "platformGold");
            fieldMapping.put("totalPlatformGold", "totalPlatformGold");

            // 配置汇总信息（按照原有的5行布局）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                // 第一行：1个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("每日Admin量化值")
                        .value(adminDailyQuantifyValue)
                        .build(),
                        UniversalExcelExporter.SummaryItem.builder()
                        .name("每日累计Admin量化值")
                        .value(adminDailyQuantizationValue)
                        .build()
                ),
                // 第二行：2个统计项
                Arrays.asList(
                        UniversalExcelExporter.SummaryItem.builder()
                                .name("每日量化值累计")
                                .value(everydayTotalQuantizationValue)
                                .build(),
                        UniversalExcelExporter.SummaryItem.builder()
                                .name("总量化值累计")
                                .value(allTotalQuantizationValue)
                                .build()
                ),
                // 第三行：2个统计项
                Arrays.asList(
                        UniversalExcelExporter.SummaryItem.builder()
                                .name("每日量化累计")
                                .value(everydayTotalCreditValue)
                                .build(),
                        UniversalExcelExporter.SummaryItem.builder()
                                .name("总量化累计")
                                .value(allTotalCreditValue)
                                .build()
                ),
                // 第四行：2个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("每日平台补贴金")
                        .value(todayTotalPlatformGold)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计平台补贴金")
                        .value(totalPlatformGold)
                        .build()
                ),
                // 第五行：2个统计项
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("每日平台促销金")
                        .value(todayTotalPromotionGold)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计平台促销金")
                        .value(totalPromotionGold)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<QuantizationValue> config = UniversalExcelExporter.ExportConfig.<QuantizationValue>builder()
                    .dataList(dataList)
                    .entityClass(QuantizationValue.class)
                    .fileName("量化值")
                    .sheetName("量化值")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出量化值异常", e);
        }
    }
}
